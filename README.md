# Towasl Backend - Firebase Functions

A secure SMS OTP authentication system built with Firebase Functions and Msegat SMS service.

## Overview

This project provides a complete backend solution for SMS-based user authentication using:
- **Firebase Functions** for serverless backend logic
- **Firestore** for secure data storage
- **Firebase Auth** for user session management
- **Msegat SMS API** for OTP delivery

## Features

- 🔐 **Secure OTP Generation**: 4-digit OTP with 5-minute expiry
- 📱 **SMS Delivery**: Integration with Msegat SMS service
- 👤 **User Management**: Automatic user creation and authentication
- 🔒 **Security**: Comprehensive input validation and CORS protection
- 📊 **Firestore Integration**: Secure data storage with proper access rules
- 🛡️ **Credential Security**: No hardcoded credentials, environment-based configuration

## Quick Start

1. **Install Dependencies**
   ```bash
   cd functions
   npm install
   ```

2. **Configure Environment**
   ```bash
   # Msegat SMS Configuration
   firebase functions:config:set msegat.username="YOUR_MSEGAT_USERNAME"
   firebase functions:config:set msegat.api_key="YOUR_MSEGAT_API_KEY"
   firebase functions:config:set msegat.sender_id="ASHKAL"
   firebase functions:config:set msegat.message_template="رمز التحقق: xxxx"

   # API Security Configuration (IMPORTANT)
   API_KEY=$(openssl rand -hex 16)
   echo "🔑 Your API Key: $API_KEY"
   echo "📝 Save this key for your Flutter app!"
   firebase functions:config:set api.key="$API_KEY"
   firebase functions:config:set api.require_key="true"
   ```
   ⚠️ Replace with your actual Msegat credentials and **save the generated API key**

3. **Deploy**
   ```bash
   ./deploy.sh
   ```

## API Endpoints

### Send OTP
**Endpoint**: `POST /sendOtp`

**Request**:
```http
POST /sendOtp
Content-Type: application/json

{
  "countryCode": "+966",
  "mobile": "512345678"
}
```

**Response**:
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "expiryTime": 1703123456789
}
```

**Error Response**:
```json
{
  "error": "Invalid phone number format"
}
```

### Verify OTP & Login
**Endpoint**: `POST /verifyOtpAndSignupLogin`

**Request**:
```http
POST /verifyOtpAndSignupLogin
Content-Type: application/json

{
  "countryCode": "+966",
  "mobile": "512345678",
  "otp": "1234"
}
```

**Request with Optional User Data**:
```http
POST /verifyOtpAndSignupLogin
Content-Type: application/json

{
  "countryCode": "+966",
  "mobile": "512345678",
  "otp": "1234",
  "userData": {
    "deviceInfo": "iPhone 15 Pro",
    "appVersion": "1.0.0"
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "customToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "uid": "firebase_user_uid",
    "phoneNumber": "+************",
    "countryCode": "+966",
    "mobile": "512345678",
    "duid": "A1B2C3" // Displayed User ID to Display later on the app to identify the users for privacy reasons
  }
}
```

**Error Response**:
```json
{
  "error": "Invalid OTP"
}
```

## Documentation

- **Setup Instructions**: [SETUP.md](SETUP.md)
- **Security Guidelines**: [SECURITY.md](SECURITY.md) - **Important security practices**
- **Flutter Integration**: [FLUTTER_INTEGRATION.md](FLUTTER_INTEGRATION.md) - **Complete Flutter setup guide**

## Project Structure

```
towasl-backend/
├── functions/           # Firebase Functions source code
├── firestore.rules     # Database security rules
├── firebase.json       # Firebase configuration
└── deploy.sh          # Deployment script
```